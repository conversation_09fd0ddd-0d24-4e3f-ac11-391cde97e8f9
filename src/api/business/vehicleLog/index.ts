import { defHttp } from '@/utils/http/axios';
import { ID, PageQuery, commonExport } from '@/api/base';
import { VehicleLog } from './model';

enum Api {
  root = '/business/vehicleLog',
  vehicleLogList = '/business/vehicleLog/list',
  vehicleLogExport = '/business/vehicleLog/export',
}

export function vehicleLogList(params?: PageQuery) {
  return defHttp.get<VehicleLog>({ url: Api.vehicleLogList, params });
}

export function vehicleLogInfo(vehicleLogId: ID) {
  return defHttp.get<VehicleLog>({ url: Api.root + '/' + vehicleLogId });
}

export function vehicleLogExport(data: any) {
  return commonExport(Api.vehicleLogExport, data);
}
