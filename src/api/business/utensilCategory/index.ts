import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { UtensilCategory } from './model';

enum Api {
  root = '/business/utensilCategory',
  utensilCategorySelect = '/business/utensilCategory/optionSelect',
  utensilCategoryList = '/business/utensilCategory/list',
  utensilCategoryExport = '/business/utensilCategory/export',
}

export function utensilCategoryList(params?: PageQuery) {
  return defHttp.get<UtensilCategory>({ url: Api.utensilCategoryList, params });
}

export function utensilCategoryInfo(utensilCategoryId: ID) {
  return defHttp.get<UtensilCategory>({ url: Api.root + '/' + utensilCategoryId });
}

export function utensilCategoryExport(data: any) {
  return commonExport(Api.utensilCategoryExport, data);
}

export function utensilCategoryAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function utensilCategoryUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function utensilCategoryRemove(utensilCategoryIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + utensilCategoryIds });
}

export function utensilCategoryOptionSelect() {
  return defHttp.get({ url: Api.utensilCategorySelect });
}
