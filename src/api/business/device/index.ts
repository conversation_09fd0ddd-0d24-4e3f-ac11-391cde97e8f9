import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { Device } from './model';

enum Api {
  root = '/business/device',
  queryLocationTree = '/business/device/queryLocationTree',
  queryGatewayList = '/business/device/queryGatewayList',
  deviceList = '/business/device/list',
  deviceExport = '/business/device/export',
  control = '/business/device/control',
}

export function deviceList(params?: PageQuery) {
  return defHttp.get<Device>({ url: Api.deviceList, params });
}

export function deviceInfo(deviceId: ID) {
  return defHttp.get<Device>({ url: Api.root + '/' + deviceId });
}

export function deviceExport(data: any) {
  return commonExport(Api.deviceExport, data);
}

export function deviceAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function deviceUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function deviceRemove(deviceIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + deviceIds });
}

export function queryLocationTree(params?: any) {
  return defHttp.get({ url: Api.queryLocationTree, params });
}

export function queryGatewayList(params?: any) {
  return defHttp.get({ url: Api.queryGatewayList, params });
}

export function control(data: any) {
  return defHttp.post<void>({ url: Api.control, data });
}
