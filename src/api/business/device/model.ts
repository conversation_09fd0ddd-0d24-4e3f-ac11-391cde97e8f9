export interface Device {
  parentTypeIds: number[];
  deviceTypeId: number;
  deviceId: number;
  stationName: string;
  warehouseName: string;
  deviceTypeName: string;
  deviceName: string;
  deviceSerial: string;
  gatewayId: number;
  gatewayName: string;
  locationName: string;
  cabinetName: string;
  connectType: string;
  deviceIp: string;
  devicePort: number;
  deviceAccount: string;
  devicePassword: string;
  remark: string;
  createTime: string;
  currentValue: number | null;
  locationPositionId: number;
  locationPositionIdArr: any;
}

export interface LocationPosition {
  label: string;
  value: number;
  children: any | null;
}
