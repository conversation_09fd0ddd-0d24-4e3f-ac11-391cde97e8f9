import { defHttp } from '@/utils/http/axios';

enum Api {
  startCheckStore = '/business/check/startCheckStore',
  checkStore = '/business/check/checkStore',
  endCheckStore = '/business/check/endCheckStore',
  startCheckScrap = '/business/check/startCheckScrap',
  checkScrap = '/business/check/checkScrap',
  endCheckScrap = '/business/check/endCheckScrap',
  startCheckReceive = '/business/check/startCheckReceive',
  checkReceive = '/business/check/checkReceive',
  endCheckReceive = '/business/check/endCheckReceive',
  startCheckReturn = '/business/check/startCheckReturn',
  checkReturn = '/business/check/checkReturn',
  endCheckReturn = '/business/check/endCheckReturn',
}

export function startCheckStore() {
  return defHttp.post({ url: Api.startCheckStore });
}

export function checkStore(data?: any) {
  return defHttp.post({ url: Api.checkStore, data });
}

export function endCheckStore() {
  return defHttp.post({ url: Api.endCheckStore });
}

export function startCheckScrap() {
  return defHttp.post({ url: Api.startCheckScrap });
}

export function checkScrap() {
  return defHttp.get({ url: Api.checkScrap });
}

export function endCheckScrap() {
  return defHttp.post({ url: Api.endCheckScrap });
}

export function startCheckReceive(data: any) {
  return defHttp.post({ url: Api.startCheckReceive, data });
}

export function checkReceive() {
  return defHttp.get({ url: Api.checkReceive });
}

export function endCheckReceive() {
  return defHttp.post({ url: Api.endCheckReceive });
}

export function startCheckReturn() {
  return defHttp.post({ url: Api.startCheckReturn });
}

export function checkReturn() {
  return defHttp.get({ url: Api.checkReturn });
}

export function endCheckReturn() {
  return defHttp.post({ url: Api.endCheckReturn });
}
