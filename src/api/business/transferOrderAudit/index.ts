import { defHttp } from '@/utils/http/axios';
import { ID, PageQuery } from '@/api/base';
import { TransferOrder } from './model';

enum Api {
  root = '/business/transferOrder',
  transferOrderListAudit = '/business/transferOrder/listAudit',
  transferOrderAudit = '/business/transferOrder/audit',
  transferOrderGetAuditRecord = '/business/transferOrder/getAuditRecord',
}

export function transferOrderListAudit(params?: PageQuery) {
  return defHttp.get<TransferOrder>({ url: Api.transferOrderListAudit, params });
}

export function transferOrderInfo(transferOrderId: ID) {
  return defHttp.get<TransferOrder>({ url: Api.root + '/' + transferOrderId });
}

export function transferOrderAudit(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.transferOrderAudit, data });
}

export function transferOrderGetAuditRecord(params?: any) {
  return defHttp.get<any>({ url: Api.transferOrderGetAuditRecord, params });
}
