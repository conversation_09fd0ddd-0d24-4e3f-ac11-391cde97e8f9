import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { Vehicle } from './model';

enum Api {
  root = '/business/vehicle',
  vehicleList = '/business/vehicle/list',
  vehicleExport = '/business/vehicle/export',
}

export function vehicleList(params?: PageQuery) {
  return defHttp.get<Vehicle>({ url: Api.vehicleList, params });
}

export function vehicleInfo(vehicleId: ID) {
  return defHttp.get<Vehicle>({ url: Api.root + '/' + vehicleId });
}

export function vehicleExport(data: any) {
  return commonExport(Api.vehicleExport, data);
}

export function vehicleAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function vehicleUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function vehicleRemove(vehicleIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + vehicleIds });
}
