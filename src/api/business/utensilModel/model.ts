export interface UtensilModel {
  utensilModelId: number;
  utensilModelName: string;
  hasIdentificationCode: string;
  utensilCategoryName: string;
  utensilSpec: string;
  materialCode: string;
  materialName: string;
  stockQty: number;
  alarmValue: number;
  testCycle: number;
  isNeedClean: string;
  isNeedDetection: string;
  barcodeLabelQty: number;
  voltageLevel: string;
  manufacturer: string;
  manufacturerPhone: string;
  manufacturerAddress: string;
  utensilModelPic: string;
  remark: string;
  createTime: string;
}
