import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { UtensilModel } from './model';

enum Api {
  root = '/business/utensilModel',
  utensilModelSelect = '/business/utensilModel/optionSelect',
  utensilModelList = '/business/utensilModel/list',
  utensilModelExport = '/business/utensilModel/export',
}

export function utensilModelList(params?: PageQuery) {
  return defHttp.get<UtensilModel>({ url: Api.utensilModelList, params });
}

export function utensilModelInfo(utensilModelId: ID) {
  return defHttp.get<UtensilModel>({ url: Api.root + '/' + utensilModelId });
}

export function utensilModelExport(data: any) {
  return commonExport(Api.utensilModelExport, data);
}

export function utensilModelAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function utensilModelUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function utensilModelRemove(utensilModelIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + utensilModelIds });
}

export function utensilModelOptionSelect(params?: any) {
  return defHttp.get({ url: Api.utensilModelSelect, params });
}
