import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { Cabinet } from './model';

enum Api {
  root = '/business/cabinet',
  cabinetSelect = '/business/cabinet/optionSelect',
  cabinetList = '/business/cabinet/list',
  cabinetExport = '/business/cabinet/export',
}

export function cabinetList(params?: PageQuery) {
  return defHttp.get<Cabinet>({ url: Api.cabinetList, params });
}

export function cabinetInfo(cabinetId: ID) {
  return defHttp.get<Cabinet>({ url: Api.root + '/' + cabinetId });
}

export function cabinetExport(data: any) {
  return commonExport(Api.cabinetExport, data);
}

export function cabinetAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function cabinetUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function cabinetRemove(cabinetIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + cabinetIds });
}

export function cabinetOptionSelect(params?: any) {
  return defHttp.get({ url: Api.cabinetSelect, params });
}
