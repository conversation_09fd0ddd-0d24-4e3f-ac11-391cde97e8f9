import { defHttp } from '@/utils/http/axios';
import { ID, PageQuery } from '@/api/base';
import { ScrapOrder } from './model';

enum Api {
  root = '/business/scrapOrder',
  scrapOrderListAudit = '/business/scrapOrder/listAudit',
  scrapOrderAudit = '/business/scrapOrder/audit',
  scrapOrderGetAuditRecord = '/business/scrapOrder/getAuditRecord',
}

export function scrapOrderListAudit(params?: PageQuery) {
  return defHttp.get<ScrapOrder>({ url: Api.scrapOrderListAudit, params });
}

export function scrapOrderInfo(scrapOrderId: ID) {
  return defHttp.get<ScrapOrder>({ url: Api.root + '/' + scrapOrderId });
}

export function scrapOrderAudit(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.scrapOrderAudit, data });
}

export function scrapOrderGetAuditRecord(params?: any) {
  return defHttp.get<any>({ url: Api.scrapOrderGetAuditRecord, params });
}
