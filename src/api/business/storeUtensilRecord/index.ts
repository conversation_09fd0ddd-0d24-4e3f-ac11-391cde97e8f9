import { defHttp } from '@/utils/http/axios';
import { ID, PageQuery, commonExport } from '@/api/base';
import { StoreUtensilRecord } from './model';

enum Api {
  root = '/business/storeUtensilRecord',
  storeUtensilRecordList = '/business/storeUtensilRecord/list',
  storeUtensilRecordExport = '/business/storeUtensilRecord/export',
}

export function storeUtensilRecordList(params?: PageQuery) {
  return defHttp.get<StoreUtensilRecord>({ url: Api.storeUtensilRecordList, params });
}

export function storeUtensilRecordInfo(storeUtensilRecordId: ID) {
  return defHttp.get<StoreUtensilRecord>({ url: Api.root + '/' + storeUtensilRecordId });
}

export function storeUtensilRecordExport(data: any) {
  return commonExport(Api.storeUtensilRecordExport, data);
}

export function storeUtensilRecordAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}
