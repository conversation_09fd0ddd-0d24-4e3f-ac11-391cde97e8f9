import { defHttp } from '@/utils/http/axios';
import { ID, PageQuery, commonExport } from '@/api/base';
import { AlarmLog } from './model';

enum Api {
  root = '/business/alarmLog',
  alarmLogList = '/business/alarmLog/list',
  alarmLogExport = '/business/alarmLog/export',
}

export function alarmLogList(params?: PageQuery) {
  return defHttp.get<AlarmLog>({ url: Api.alarmLogList, params });
}

export function alarmLogInfo(alarmLogId: ID) {
  return defHttp.get<AlarmLog>({ url: Api.root + '/' + alarmLogId });
}

export function alarmLogExport(data: any) {
  return commonExport(Api.alarmLogExport, data);
}
