import { defHttp } from '@/utils/http/axios';
import { ID, PageQuery, commonExport } from '@/api/base';
import { Utensil } from './model';

enum Api {
  root = '/business/utensil',
  utensilSelect = '/business/utensil/optionSelect',
  utensilList = '/business/utensil/list',
  utensilExport = '/business/utensil/export',
  utensilUpdateTestDate = '/business/utensil/updateTestDate',
  utensilUpdateIdentificationCode = '/business/utensil/updateIdentificationCode',
  utensilLifeCircle = '/business/utensilLifeCircle/list',
}

export function utensilList(params?: PageQuery) {
  return defHttp.get<Utensil>({ url: Api.utensilList, params });
}

export function utensilInfo(utensilId: ID) {
  return defHttp.get<Utensil>({ url: Api.root + '/' + utensilId });
}

export function utensilExport(data: any) {
  return commonExport(Api.utensilExport, data);
}

export function utensilUpdateTestDate(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.utensilUpdateTestDate, data });
}

export function utensilOptionSelect(params?: any) {
  return defHttp.get({ url: Api.utensilSelect, params });
}

export function utensilLifeCircle(params?: any) {
  return defHttp.get({ url: Api.utensilLifeCircle, params });
}

export function utensilRemove(utensilId: ID) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + utensilId });
}

export function utensilUpdateIdentificationCode(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.utensilUpdateIdentificationCode, data });
}
