export interface Utensil {
  utensilId: number;
  stationName: string;
  warehouseName: string;
  shelfName: string;
  locationName: string;
  utensilName: string;
  utensilCode: string;
  utensilModelName: string;
  identificationCode: string | undefined | null;
  utensilStatus: string;
  testDate: string;
  usageCount: number;
  remark: string;
  createTime: string;
  hasIdentificationCode?: string;
  checkResult?: string;
}
