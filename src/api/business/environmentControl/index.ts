import { defHttp } from '@/utils/http/axios';

enum Api {
  queryEnvControlInfo = '/business/env_control/info',
  envControl = '/business/env_control/control',
  queryStations = '/business/index/queryStations',
   queryWarehouseInfoList = '/business/index/queryWarehouseInfoList',
}

export function queryEnvControlInfo(stationId: any, warehouseId: any) {
  return defHttp.get({ url: Api.queryEnvControlInfo + '?stationId=' + stationId + '&warehouseId=' + warehouseId });
}

export function envControl(data: any) {
  return defHttp.post({
    url: Api.envControl,
    data,
  });
}

export function stationOptionSelect() {
  return defHttp.get({ url: Api.queryStations });
}

export function queryWarehouseInfoList(stationId: any) {
  return defHttp.get({
    url: Api.queryWarehouseInfoList + '?stationId=' + stationId,
  });
}