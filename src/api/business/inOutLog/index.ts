import { defHttp } from '@/utils/http/axios';
import { ID, PageQuery, commonExport } from '@/api/base';
import { InOutLog } from './model';

enum Api {
  root = '/business/inOutLog',
  inOutLogList = '/business/inOutLog/list',
  inOutLogExport = '/business/inOutLog/export',
}

export function inOutLogList(params?: PageQuery) {
  return defHttp.get<InOutLog>({ url: Api.inOutLogList, params });
}

export function inOutLogInfo(inOutLogId: ID) {
  return defHttp.get<InOutLog>({ url: Api.root + '/' + inOutLogId });
}

export function inOutLogExport(data: any) {
  return commonExport(Api.inOutLogExport, data);
}
