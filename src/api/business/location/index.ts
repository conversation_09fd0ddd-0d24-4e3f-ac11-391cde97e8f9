import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { Location } from './model';

enum Api {
  root = '/business/location',
  locationBatchAdd = '/business/location/batchAdd',
  locationSelect = '/business/location/optionSelect',
  locationList = '/business/location/list',
  locationExport = '/business/location/export',
}

export function locationList(params?: PageQuery) {
  return defHttp.get<Location>({ url: Api.locationList, params });
}

export function locationInfo(locationId: ID) {
  return defHttp.get<Location>({ url: Api.root + '/' + locationId });
}

export function locationExport(data: any) {
  return commonExport(Api.locationExport, data);
}

export function locationBatchAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.locationBatchAdd, data });
}

export function locationAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function locationUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function locationRemove(locationIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + locationIds });
}

export function locationOptionSelect(params?: any) {
  return defHttp.get({ url: Api.locationSelect, params });
}
