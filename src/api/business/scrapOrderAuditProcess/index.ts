import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery } from '@/api/base';
import { ScrapOrderAuditProcess } from './model';

enum Api {
  root = '/business/scrapOrderAuditProcess',
  scrapOrderAuditProcessList = '/business/scrapOrderAuditProcess/list',
}

export function scrapOrderAuditProcessList(params?: PageQuery) {
  return defHttp.get<ScrapOrderAuditProcess>({ url: Api.scrapOrderAuditProcessList, params });
}

export function scrapOrderAuditProcessInfo(scrapOrderAuditProcessId: ID) {
  return defHttp.get<ScrapOrderAuditProcess>({ url: Api.root + '/' + scrapOrderAuditProcessId });
}

export function scrapOrderAuditProcessAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function scrapOrderAuditProcessUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function scrapOrderAuditProcessRemove(scrapOrderAuditProcessIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + scrapOrderAuditProcessIds });
}
