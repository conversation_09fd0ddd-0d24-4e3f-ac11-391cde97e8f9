import { defHttp } from '@/utils/http/axios';
import { StationDataPermission } from './model';

enum Api {
  queryByUserId = '/business/stationDataPermission/queryByUserId',
  save = '/business/stationDataPermission/save',
}

export function queryByUserId(params?: any) {
  return defHttp.get<StationDataPermission>({ url: Api.queryByUserId, params });
}

export function save(data: any) {
  return defHttp.post<void>({ url: Api.save, data });
}
