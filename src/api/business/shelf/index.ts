import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { Shelf } from './model';

enum Api {
  root = '/business/shelf',
  shelfSelect = '/business/shelf/optionSelect',
  shelfList = '/business/shelf/list',
  shelfExport = '/business/shelf/export',
}

export function shelfList(params?: PageQuery) {
  return defHttp.get<Shelf>({ url: Api.shelfList, params });
}

export function shelfInfo(shelfId: ID) {
  return defHttp.get<Shelf>({ url: Api.root + '/' + shelfId });
}

export function shelfExport(data: any) {
  return commonExport(Api.shelfExport, data);
}

export function shelfAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function shelfUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function shelfRemove(shelfIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + shelfIds });
}

export function shelfOptionSelect(params?: any) {
  return defHttp.get({ url: Api.shelfSelect, params });
}
