import { defHttp } from '@/utils/http/axios';
import { ID, PageQuery, commonExport } from '@/api/base';
import { Station } from './model';

enum Api {
  root = '/business/station',
  stationSelect = '/business/station/optionSelect',
  stationList = '/business/station/list',
  stationExport = '/business/station/export',
}

export function stationList(params?: PageQuery) {
  return defHttp.get<Station>({ url: Api.stationList, params });
}

export function stationInfo(stationId: ID) {
  return defHttp.get<Station>({ url: Api.root + '/' + stationId });
}

export function stationExport(data: any) {
  return commonExport(Api.stationExport, data);
}

export function stationAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function stationUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function stationOptionSelect() {
  return defHttp.get({ url: Api.stationSelect });
}
