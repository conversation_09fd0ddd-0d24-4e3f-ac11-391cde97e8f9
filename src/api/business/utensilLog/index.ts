import { defHttp } from '@/utils/http/axios';
import { ID, PageQuery, commonExport } from '@/api/base';
import { UtensilLog } from './model';

enum Api {
  root = '/business/utensilLog',
  utensilLogList = '/business/utensilLog/list',
  utensilLogExport = '/business/utensilLog/export',
}

export function utensilLogList(params?: PageQuery) {
  return defHttp.get<UtensilLog>({ url: Api.utensilLogList, params });
}

export function utensilLogInfo(utensilLogId: ID) {
  return defHttp.get<UtensilLog>({ url: Api.root + '/' + utensilLogId });
}

export function utensilLogExport(data: any) {
  return commonExport(Api.utensilLogExport, data);
}
