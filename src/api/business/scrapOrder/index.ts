import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { ScrapOrder } from './model';

enum Api {
  root = '/business/scrapOrder',
  scrapOrderList = '/business/scrapOrder/list',
  scrapOrderExport = '/business/scrapOrder/export',
  scrapOrderSubmitAudit = '/business/scrapOrder/submit',
  execScrap = '/business/scrapOrder/execScrap',
}

export function scrapOrderList(params?: PageQuery) {
  return defHttp.get<ScrapOrder>({ url: Api.scrapOrderList, params });
}

export function scrapOrderInfo(scrapOrderId: ID) {
  return defHttp.get<ScrapOrder>({ url: Api.root + '/' + scrapOrderId });
}

export function scrapOrderExport(data: any) {
  return commonExport(Api.scrapOrderExport, data);
}

export function scrapOrderAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function scrapOrderUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function scrapOrderRemove(scrapOrderIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + scrapOrderIds });
}

export function scrapOrderSubmitAudit(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.scrapOrderSubmitAudit, data });
}

export function execScrap(scrapOrderId: ID) {
  return defHttp.get({ url: Api.execScrap + '?scrapOrderId=' + scrapOrderId });
}
