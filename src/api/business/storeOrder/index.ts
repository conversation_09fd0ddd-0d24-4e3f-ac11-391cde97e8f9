import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { StoreOrder } from './model';

enum Api {
  root = '/business/storeOrder',
  storeOrderList = '/business/storeOrder/list',
  storeOrderExport = '/business/storeOrder/export',
  execStore = '/business/storeOrder/execStore',
}

export function storeOrderList(params?: PageQuery) {
  return defHttp.get<StoreOrder>({ url: Api.storeOrderList, params });
}

export function storeOrderInfo(storeOrderId: ID) {
  return defHttp.get<StoreOrder>({ url: Api.root + '/' + storeOrderId });
}

export function storeOrderExport(data: any) {
  return commonExport(Api.storeOrderExport, data);
}

export function storeOrderAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function storeOrderUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function storeOrderRemove(storeOrderIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + storeOrderIds });
}

export function execStore(data: any) {
  return defHttp.postWithMsg({ url: Api.execStore, data });
}
