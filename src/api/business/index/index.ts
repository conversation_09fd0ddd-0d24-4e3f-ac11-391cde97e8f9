import { defHttp } from '@/utils/http/axios';
import { ID } from '@/api/base';
import { StationInfo } from './model';

enum Api {
  queryStations = '/business/index/queryStations',
  queryStationInfo = '/business/index/queryStationInfo',
  queryWarehouseInfoList = '/business/index/queryWarehouseInfoList', // 获取所有库房
  getEnvironmentInfo = '/business/index/getEnvironmentInfo',
  utensilTestWarning = '/business/index/utensilTestWarning',
  utensilSummary = '/business/index/utensilSummary',
  workOrderWarning = '/business/index/workOrderWarning',
  selectUtensil = '/business/warehouse/optionSelectUtensil', // 只获取工器具库房
}

export function stationOptionSelect() {
  return defHttp.get({ url: Api.queryStations });
}

export function queryStationInfo(stationId: any) {
  return defHttp.get<StationInfo>({ url: Api.queryStationInfo + '?stationId=' + stationId });
}

export function queryWarehouseInfoList(stationId: any) {
  return defHttp.get({
    url: Api.queryWarehouseInfoList + '?stationId=' + stationId,
  });
}

// 只获取工器具库房
export function selectUtensil(stationId: any) {
  return defHttp.get({
    url: Api.selectUtensil + '?stationId=' + stationId,
  });
}

export function getEnvironmentInfo(warehouseId: ID) {
  return defHttp.get({ url: Api.getEnvironmentInfo + '?warehouseId=' + warehouseId });
}

export function utensilTestWarning(warehouseId: ID) {
  return defHttp.get({ url: Api.utensilTestWarning + '?warehouseId=' + warehouseId });
}

export function utensilSummary(warehouseId: ID) {
  return defHttp.get({ url: Api.utensilSummary + '?warehouseId=' + warehouseId });
}

export function workOrderWarning(warehouseId: ID) {
  return defHttp.get({ url: Api.workOrderWarning + '?warehouseId=' + warehouseId });
}
