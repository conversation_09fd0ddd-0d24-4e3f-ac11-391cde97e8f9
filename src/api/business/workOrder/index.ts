import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { WorkOrder } from './model';

enum Api {
  root = '/business/workOrder',
  workOrderList = '/business/workOrder/list',
  workOrderExport = '/business/workOrder/export',
  workOrderWaitReceiveList = '/business/workOrder/listWaitReceive',
  workOrderWaitReceiveExport = '/business/workOrder/exportWaitReceive',
  workOrderWaitReturnList = '/business/workOrder/listWaitReturn',
  workOrderWaitReturnExport = '/business/workOrder/exportWaitReturn',
  executeReceive = '/business/workOrder/executeReceive',
  executeReturn = '/business/workOrder/executeReturn',
}

export function workOrderList(params?: PageQuery) {
  return defHttp.get<WorkOrder>({ url: Api.workOrderList, params });
}

export function workOrderInfo(workOrderId: ID) {
  return defHttp.get<WorkOrder>({ url: Api.root + '/' + workOrderId });
}

export function workOrderExport(data: any) {
  return commonExport(Api.workOrderExport, data);
}

export function workOrderAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function workOrderUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function workOrderRemove(workOrderIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + workOrderIds });
}

export function workOrderWaitReceiveList(params?: PageQuery) {
  return defHttp.get<WorkOrder>({ url: Api.workOrderWaitReceiveList, params });
}

export function workOrderWaitReceiveExport(data: any) {
  return commonExport(Api.workOrderWaitReceiveExport, data);
}

export function workOrderWaitReturnList(params?: PageQuery) {
  return defHttp.get<WorkOrder>({ url: Api.workOrderWaitReturnList, params });
}

export function workOrderWaitReturnExport(data: any) {
  return commonExport(Api.workOrderWaitReturnExport, data);
}

export function executeReceive(data?: any) {
  return defHttp.post<any>({ url: Api.executeReceive, data });
}

export function executeReturn(data?: any) {
  return defHttp.post<any>({ url: Api.executeReturn, data });
}
