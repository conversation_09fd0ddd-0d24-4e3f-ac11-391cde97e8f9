export interface WorkOrder {
  workOrderId: number;
  isNormal: string;
  stationName: string;
  warehouseName: string;
  cabinetName: string;
  workOrderCode: string;
  workOrderStatus: string;
  workOrderCabinetStatus: string;
  workOrderType: string;
  relatedTransferOrderId: number;
  taskType: string;
  batchNumber: string;
  businessId: string;
  workOrderTemplate: string;
  responsiblePerson: string;
  utensilDemandList: any;
  utensilVos: any;
  remark: string;
  createTime: string;
}
