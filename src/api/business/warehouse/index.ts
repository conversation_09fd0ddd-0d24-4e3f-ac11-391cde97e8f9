import { defHttp } from '@/utils/http/axios';
import { ID, PageQuery, commonExport } from '@/api/base';
import { Warehouse } from './model';

enum Api {
  root = '/business/warehouse',
  warehouseStatusChange = '/business/warehouse/changeStatus',
  warehouseSelect = '/business/warehouse/optionSelect',
  warehouseSelectUtensil = '/business/warehouse/optionSelectUtensil',
  warehouseSelectVehicle = '/business/warehouse/optionSelectVehicle',
  warehouseList = '/business/warehouse/list',
  warehouseExport = '/business/warehouse/export',
}

export function warehouseList(params?: PageQuery) {
  return defHttp.get<Warehouse>({ url: Api.warehouseList, params });
}

export function warehouseInfo(warehouseId: ID) {
  return defHttp.get<Warehouse>({ url: Api.root + '/' + warehouseId });
}

export function warehouseExport(data: any) {
  return commonExport(Api.warehouseExport, data);
}

export function warehouseAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function warehouseUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function warehouseStatusChange(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.warehouseStatusChange, data });
}

export function warehouseOptionSelect(params?: any) {
  return defHttp.get({ url: Api.warehouseSelect, params });
}

export function warehouseOptionSelectUtensil(params?: any) {
  return defHttp.get({ url: Api.warehouseSelectUtensil, params });
}

export function warehouseOptionSelectVehicle(params?: any) {
  return defHttp.get({ url: Api.warehouseSelectVehicle, params });
}
