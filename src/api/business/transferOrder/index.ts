import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { TransferOrder } from './model';

enum Api {
  root = '/business/transferOrder',
  transferOrderList = '/business/transferOrder/list',
  transferOrderExport = '/business/transferOrder/export',
  transferOrderSubmitAudit = '/business/transferOrder/submit',
  transferOrderGetProgressRecord = '/business/transferOrder/getProgressRecord',
}

export function transferOrderList(params?: PageQuery) {
  return defHttp.get<TransferOrder>({ url: Api.transferOrderList, params });
}

export function transferOrderInfo(transferOrderId: ID) {
  return defHttp.get<TransferOrder>({ url: Api.root + '/' + transferOrderId });
}

export function transferOrderExport(data: any) {
  return commonExport(Api.transferOrderExport, data);
}

export function transferOrderAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function transferOrderUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function transferOrderRemove(transferOrderIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + transferOrderIds });
}

export function transferOrderSubmitAudit(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.transferOrderSubmitAudit, data });
}

export function transferOrderGetProgressRecord(params?: any) {
  return defHttp.get<any>({ url: Api.transferOrderGetProgressRecord, params });
}
