import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { UtensilSpec } from './model';

enum Api {
  root = '/business/utensilSpec',
  utensilSpecSelect = '/business/utensilSpec/optionSelect',
  utensilSpecList = '/business/utensilSpec/list',
  utensilSpecExport = '/business/utensilSpec/export',
}

export function utensilSpecList(params?: PageQuery) {
  return defHttp.get<UtensilSpec>({ url: Api.utensilSpecList, params });
}

export function utensilSpecInfo(utensilSpecId: ID) {
  return defHttp.get<UtensilSpec>({ url: Api.root + '/' + utensilSpecId });
}

export function utensilSpecExport(data: any) {
  return commonExport(Api.utensilSpecExport, data);
}

export function utensilSpecAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function utensilSpecUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function utensilSpecRemove(utensilSpecIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + utensilSpecIds });
}

export function utensilSpecOptionSelect(params?: any) {
  return defHttp.get({ url: Api.utensilSpecSelect, params });
}
