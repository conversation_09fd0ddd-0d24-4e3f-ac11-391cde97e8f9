import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { Position } from './model';

enum Api {
  root = '/business/position',
  positionList = '/business/position/list',
  positionExport = '/business/position/export',
}

export function positionList(params?: PageQuery) {
  return defHttp.get<Position>({ url: Api.positionList, params });
}

export function positionInfo(positionId: ID) {
  return defHttp.get<Position>({ url: Api.root + '/' + positionId });
}

export function positionExport(data: any) {
  return commonExport(Api.positionExport, data);
}

export function positionAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function positionUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function positionRemove(positionIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + positionIds });
}
