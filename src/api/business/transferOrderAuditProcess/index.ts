import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery } from '@/api/base';
import { TransferOrderAuditProcess } from './model';

enum Api {
  root = '/business/transferOrderAuditProcess',
  transferOrderAuditProcessList = '/business/transferOrderAuditProcess/list',
}

export function transferOrderAuditProcessList(params?: PageQuery) {
  return defHttp.get<TransferOrderAuditProcess>({
    url: Api.transferOrderAuditProcessList,
    params,
  });
}

export function transferOrderAuditProcessInfo(transferOrderAuditProcessId: ID) {
  return defHttp.get<TransferOrderAuditProcess>({
    url: Api.root + '/' + transferOrderAuditProcessId,
  });
}

export function transferOrderAuditProcessAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function transferOrderAuditProcessUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function transferOrderAuditProcessRemove(transferOrderAuditProcessIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + transferOrderAuditProcessIds });
}
