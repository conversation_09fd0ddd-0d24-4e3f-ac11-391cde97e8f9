export interface DeviceType {
  parentId: number;
  ancestors: any;
  deviceTypeId: number;
  deviceTypeName: string;
  deviceTypePic: string;
  deviceTypeStatus: string;
  remark: string;
  createTime: string;
}

/**
 * @description: 设备类型树
 */
export interface DeviceTypeTree {
  id: number;
  key: number;
  label: string;
  parentId: number
  children?: DeviceTypeTree[];
  deviceTypeId?: number;
  deviceTypeName?: string;
}