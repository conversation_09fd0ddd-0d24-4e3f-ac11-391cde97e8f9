import { defHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, commonExport } from '@/api/base';
import { DeviceType } from './model';

enum Api {
  root = '/business/deviceType',
  deviceTypeSelect = '/business/deviceType/optionSelect',
  deviceTypeStatusChange = '/business/deviceType/changeStatus',
  deviceTypeList = '/business/deviceType/list',
  deviceTypeExport = '/business/deviceType/export',
  deviceTypeTree = '/business/deviceType/tree',
}

export function deviceTypeList(params?: PageQuery) {
  return defHttp.get<DeviceType>({ url: Api.deviceTypeList, params });
}

export function deviceTypeInfo(deviceTypeId: ID) {
  return defHttp.get<DeviceType>({ url: Api.root + '/' + deviceTypeId });
}

export function deviceTypeExport(data: any) {
  return commonExport(Api.deviceTypeExport, data);
}

export function deviceTypeAdd(data: any) {
  return defHttp.postWithMsg<void>({ url: Api.root, data });
}

export function deviceTypeUpdate(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.root, data });
}

export function deviceTypeStatusChange(data: any) {
  return defHttp.putWithMsg<void>({ url: Api.deviceTypeStatusChange, data });
}

export function deviceTypeRemove(deviceTypeIds: IDS) {
  return defHttp.deleteWithMsg({ url: Api.root + '/' + deviceTypeIds });
}

export function deviceTypeOptionSelect() {
  return defHttp.get({ url: Api.deviceTypeSelect });
}

export function deviceTypeTreeApi() {
  return defHttp.get({ url: Api.deviceTypeTree });
}
