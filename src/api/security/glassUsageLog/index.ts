import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import { 
  ArGlassUsageLogBo, 
  ArGlassUsageLogVo, 
  TableDataInfoArGlassUsageLogVo,
  GlassUsageStatsVo 
} from './model';

enum Api {
  root = '/business/glassOperationLog',
  list = '/business/glassOperationLog/list',
  export = '/business/glassOperationLog/export',
  count = '/business/glassOperationLog/count',
}

/**
 * 查询AR眼镜使用信息列表
 */
export function glassUsageLogList(params?: PageQuery) {
  return securityHttp.get<TableDataInfoArGlassUsageLogVo>({ url: Api.list, params });
}

/**
 * 获取AR眼镜使用信息详细信息
 */
export function glassUsageLogInfo(id: ID) {
  return securityHttp.get<ArGlassUsageLogVo>({ url: Api.root + '/' + id });
}

/**
 * 新增AR眼镜使用信息
 */
export function glassUsageLogAdd(data: ArGlassUsageLogBo) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

/**
 * 修改AR眼镜使用信息
 */
export function glassUsageLogUpdate(data: ArGlassUsageLogBo) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

/**
 * 删除AR眼镜使用信息
 */
export function glassUsageLogRemove(ids: IDS) {
  return securityHttp.deleteWithMsg<void>({ url: Api.root + '/' + ids });
}

/**
 * 导出AR眼镜使用信息列表
 */
export function glassUsageLogExport(data: any) {
  return securityCommonExport(Api.export, data);
}

/**
 * 获取AR眼镜使用统计数据
 */
export function glassUsageLogCount(params?: any) {
  return securityHttp.get<GlassUsageStatsVo>({ url: Api.count, params });
}
