import { BaseEntity } from '@/api/base';

/**
 * AR眼镜使用信息业务对象
 */
export interface ArGlassUsageLogBo extends BaseEntity {
  /** 主键id */
  id?: number;
  /** 设备编号（关联 AR 眼镜表） */
  deviceNo: string;
  /** 使用人员工号（关联作业人员表） */
  userId: string;
  /** 使用人员名称 */
  userName: string;
  /** 登录时间 */
  loginTime: string;
  /** 登出时间 */
  logoutTime: string;
  /** 总使用时长（分钟） */
  totalDuration: number;
  /** 站点id */
  stationId: string;
  /** 站点名称 */
  stationName: string;
  /** 备注 */
  remark?: string;
}

/**
 * AR眼镜使用信息视图对象
 */
export interface ArGlassUsageLogVo {
  /** 主键id */
  id: number;
  /** 设备编号（关联 AR 眼镜表） */
  deviceNo: string;
  /** 使用人员工号（关联作业人员表） */
  userId: string;
  /** 使用人员名称 */
  userName: string;
  /** 登录时间 */
  loginTime: string;
  /** 登出时间 */
  logoutTime: string;
  /** 总使用时长（分钟） */
  totalDuration: number;
  /** 站点id */
  stationId: string;
  /** 站点名称 */
  stationName: string;
  /** 备注 */
  remark?: string;
}

/**
 * 表格分页数据对象
 */
export interface TableDataInfoArGlassUsageLogVo {
  /** 总记录数 */
  total: number;
  /** 列表数据 */
  rows: ArGlassUsageLogVo[];
  /** 消息状态码 */
  code: number;
  /** 消息内容 */
  msg: string;
}

/**
 * AR眼镜使用统计数据
 */
export interface GlassUsageStatsVo {
  /** 总使用次数 */
  totalUesd: number;
  /** 总设备数 */
  totalDevices: number;
  /** 总使用时长（分钟） */
  totalDuration: number;
  /** 总使用时长字符串 */
  totalDurationStr: string;
}
