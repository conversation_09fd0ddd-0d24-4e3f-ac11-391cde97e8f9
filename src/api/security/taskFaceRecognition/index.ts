import { securityHttp } from '@/utils/http/axios';
import { ID, PageQuery } from '@/api/base';
import { TaskFaceRecognition, TableDataInfoTaskFaceRecognitionVo } from './model';

enum Api {
  root = '/business/taskFaceRecognition',
  list = '/business/taskFaceRecognition/list',
}

/**
 * 查询任务人脸识别列表
 */
export function taskFaceRecognitionList(params?: PageQuery) {
  return securityHttp.get<TableDataInfoTaskFaceRecognitionVo>({ url: Api.list, params });
}

/**
 * 获取任务人脸识别详细信息
 */
export function taskFaceRecognitionInfo(id: ID) {
  return securityHttp.get<TaskFaceRecognition>({ url: Api.root + '/' + id });
}
