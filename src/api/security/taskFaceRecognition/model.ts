import { BaseEntity } from '@/api/base';

/**
 * 任务人脸识别对象
 */
export interface TaskFaceRecognition extends BaseEntity {
  id?: number; // 主键ID
  taskId: number; // 任务ID
  taskNum?: string; // 任务编号
  stepId: number; // 步骤ID
  stepName?: string; // 步骤名称
  userNo?: string; // 用户编号
  userName?: string; // 用户姓名
  status: string; // 状态：1-不符合，2-符合
  detectImagesUrl?: string; // 检测图片地址
  detectTime?: string; // 检测时间
  remark?: string; // 备注
}

/**
 * 任务人脸识别视图对象
 */
export interface TaskFaceRecognitionVo extends TaskFaceRecognition {
  // 可以添加额外的视图字段
}

/**
 * 表格分页数据对象
 */
export interface TableDataInfoTaskFaceRecognitionVo {
  total: number; // 总记录数
  rows: TaskFaceRecognitionVo[]; // 列表数据
  code: number; // 消息状态码
  msg: string; // 消息内容
}
