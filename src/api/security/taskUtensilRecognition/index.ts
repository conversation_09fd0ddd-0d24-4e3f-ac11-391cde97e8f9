import { securityHttp } from '@/utils/http/axios';
import { ID, PageQuery } from '@/api/base';
import { TaskUtensilRecognition, TableDataInfoTaskUtensilRecognitionVo } from './model';

enum Api {
  root = '/business/taskUtensilRecognition',
  list = '/business/taskUtensilRecognition/list',
}

/**
 * 查询任务工具识别列表
 */
export function taskUtensilRecognitionList(params?: PageQuery) {
  return securityHttp.get<TableDataInfoTaskUtensilRecognitionVo>({ url: Api.list, params });
}

/**
 * 获取任务工具识别详细信息
 */
export function taskUtensilRecognitionInfo(id: ID) {
  return securityHttp.get<TaskUtensilRecognition>({ url: Api.root + '/' + id });
}
