import { BaseEntity } from '@/api/base';

/**
 * 任务工具识别对象
 */
export interface TaskUtensilRecognition extends BaseEntity {
  id?: number; // 主键ID
  taskId: number; // 任务ID
  taskNum?: string; // 任务编号
  upperStepId?: number; // 上道步骤ID
  upperStepName?: string; // 上道步骤名称
  lowerStepId?: number; // 下道步骤ID
  lowerStepName?: string; // 下道步骤名称
  utensilName?: string; // 工具名称
  upperTrackNum?: number; // 上道数量
  lowerTrackNum?: number; // 下道数量
  upperTrackDetectImagesUrl?: string; // 上道检测图片地址
  upperTrackDetectTime?: string; // 上道检测时间
  lowerTrackDetectImagesUrl?: string; // 下道检测图片地址
  lowerTrackDetectTime?: string; // 下道检测时间
  remark?: string; // 备注
  status?: string; // 状态：1-正常
}

/**
 * 任务工具识别视图对象
 */
export interface TaskUtensilRecognitionVo extends TaskUtensilRecognition {
  // 可以添加额外的视图字段
}

/**
 * 表格分页数据对象
 */
export interface TableDataInfoTaskUtensilRecognitionVo {
  total: number; // 总记录数
  rows: TaskUtensilRecognitionVo[]; // 列表数据
  code: number; // 消息状态码
  msg: string; // 消息内容
}
