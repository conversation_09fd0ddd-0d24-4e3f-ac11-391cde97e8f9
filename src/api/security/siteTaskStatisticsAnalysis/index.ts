import { securityHttp } from '@/utils/http/axios';
import {
  TaskExceptionStats,
  TaskExceptionTrendItem,
  TaskStatisticsItem,
  EventTypeDistributionItem,
  SiteTaskTrendItem,
  TaskStatisticsQuery,
} from './model';

enum Api {
  taskExceptionStats = '/business/siteTaskStatisticsAnalysis/getTaskExceptionStatistics',
  taskStatisticsBySiteAndTime = '/business/siteTaskStatisticsAnalysis/getTaskStatisticsBySiteAndTime',
  eventTypeDistribution = '/business/siteTaskStatisticsAnalysis/getEventTypeDistribution',
  siteTaskTrendChart = '/business/siteTaskStatisticsAnalysis/getSiteTaskTrendChart',
  eventTrend = '/business/siteTaskStatisticsAnalysis/getEventTrend',
}

/**
 * 获取任务异常情况统计
 */
export function getTaskExceptionStatistics(params?: TaskStatisticsQuery) {
  return securityHttp.get<TaskExceptionStats>({ url: Api.taskExceptionStats, params });
}

/**
 * 获取任务情况统计
 */
export function getTaskStatisticsBySiteAndTime(params?: TaskStatisticsQuery) {
  return securityHttp.get<TaskStatisticsItem[]>({ url: Api.taskStatisticsBySiteAndTime, params });
}

/**
 * 获取异常事件趋势
 */
export function getEventTrend(params?: TaskStatisticsQuery) {
  return securityHttp.get<TaskExceptionTrendItem[]>({ url: Api.eventTrend, params });
}

/**
 * 获取异常类型分布
 */
export function getEventTypeDistribution(params?: TaskStatisticsQuery) {
  return securityHttp.get<EventTypeDistributionItem[]>({ url: Api.eventTypeDistribution, params });
}

/**
 * 获取站点任务趋势图
 */
export function getSiteTaskTrendChart(params?: TaskStatisticsQuery) {
  return securityHttp.get<SiteTaskTrendItem[]>({ url: Api.siteTaskTrendChart, params });
}
