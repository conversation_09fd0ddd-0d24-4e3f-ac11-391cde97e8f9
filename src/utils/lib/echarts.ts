import * as echarts from 'echarts/core';
import 'echarts-gl';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ctorialBar<PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON>atter<PERSON><PERSON>,
  EffectScatter<PERSON>hart,
  GaugeChart,
} from 'echarts/charts';

import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  ParallelComponent,
  LegendComponent,
  RadarComponent,
  ToolboxComponent,
  DataZoomComponent,
  VisualMapComponent,
  TimelineComponent,
  CalendarComponent,
  GraphicComponent,
  GeoComponent,
} from 'echarts/components';

import { <PERSON><PERSON>enderer } from 'echarts/renderers';

echarts.use([
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  Parallel<PERSON>omponent,
  <PERSON>eoComponent,
  <PERSON><PERSON>hart,
  Line<PERSON>hart,
  <PERSON><PERSON>hart,
  MapChart,
  RadarChart,
  SVGRenderer,
  PictorialBar<PERSON>hart,
  RadarComponent,
  ToolboxComponent,
  DataZoomComponent,
  VisualMapComponent,
  TimelineComponent,
  Calendar<PERSON>omponent,
  GraphicComponent,
  <PERSON>atter<PERSON>hart,
  EffectScatter<PERSON>hart,
  <PERSON>auge<PERSON>hart,
]);

export default echarts;
