<template>
  <div class="test-map-container">
    <h2>地图测试页面</h2>
    <div ref="mapRef" class="map-chart" style="width: 800px; height: 600px; border: 1px solid #ccc;"></div>
    <div class="status">
      <p>地图状态: {{ mapStatus }}</p>
      <button @click="loadMap">重新加载地图</button>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import echarts from '@/utils/lib/echarts';

  const mapRef = ref();
  const mapStatus = ref('未加载');
  const { setOptions } = useECharts(mapRef);

  // 地图数据
  const mapData = [
    { name: '成都', value: [104.066, 30.572, 85], itemStyle: { color: '#26B99A' } },
    { name: '石家庄', value: [114.514, 38.042, 72], itemStyle: { color: '#3498DB' } }
  ];

  // 加载中国地图数据
  async function loadChinaMapData() {
    try {
      mapStatus.value = '加载中...';
      console.log('开始加载地图数据...');
      
      const response = await fetch('/100000_full.json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const geoJson = await response.json();
      console.log('地图数据加载成功:', geoJson);
      
      // 注册地图数据到 ECharts
      echarts.registerMap('china', geoJson);
      console.log('地图数据注册成功');
      
      // 初始化地图
      initMap();
      mapStatus.value = '加载成功';
    } catch (error) {
      console.error('加载中国地图数据失败:', error);
      mapStatus.value = `加载失败: ${error.message}`;
    }
  }

  // 初始化地图
  function initMap() {
    const option = {
      backgroundColor: '#f0f0f0',
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          if (params.seriesType === 'scatter') {
            return `${params.name}<br/>活跃度: ${params.value[2]}`;
          }
          return params.name;
        }
      },
      geo: {
        map: 'china',
        roam: true,
        zoom: 1.2,
        center: [107, 36],
        itemStyle: {
          areaColor: '#e0e0e0',
          borderColor: '#333',
          borderWidth: 1
        },
        emphasis: {
          itemStyle: {
            areaColor: '#ccc'
          }
        }
      },
      series: [
        {
          type: 'scatter',
          coordinateSystem: 'geo',
          data: mapData,
          symbolSize: function(val) {
            return Math.max(val[2] / 4, 15);
          },
          symbol: 'circle',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        }
      ]
    };
    
    console.log('设置地图选项:', option);
    setOptions(option);
  }

  function loadMap() {
    loadChinaMapData();
  }

  onMounted(() => {
    setTimeout(() => {
      loadChinaMapData();
    }, 100);
  });
</script>

<style scoped>
  .test-map-container {
    padding: 20px;
  }

  .map-chart {
    margin: 20px 0;
  }

  .status {
    margin-top: 20px;
  }

  .status button {
    padding: 8px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .status button:hover {
    background: #0056b3;
  }
</style>
