<template>
  <div class="test-map-simple">
    <h2>中国3D地图测试</h2>
    <div class="status-info">
      <p>地图状态: <span :class="statusClass">{{ mapStatus }}</span></p>
      <button @click="reloadMap" class="reload-btn">重新加载地图</button>
    </div>
    <div class="map-wrapper">
      <div ref="mapRef" class="map-chart"></div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, computed } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import echarts from '@/utils/lib/echarts';

  const mapRef = ref();
  const mapStatus = ref('未加载');
  const { setOptions } = useECharts(mapRef);

  const statusClass = computed(() => {
    switch (mapStatus.value) {
      case '加载成功':
        return 'status-success';
      case '加载中...':
        return 'status-loading';
      case '未加载':
        return 'status-pending';
      default:
        return 'status-error';
    }
  });

  // 地图数据
  const mapData = [
    { name: '成都', value: [104.066, 30.572, 85], itemStyle: { color: '#26B99A' } },
    { name: '石家庄', value: [114.514, 38.042, 72], itemStyle: { color: '#3498DB' } },
    { name: '北京', value: [116.405, 39.905, 95], itemStyle: { color: '#E74C3C' } },
    { name: '上海', value: [121.473, 31.230, 88], itemStyle: { color: '#F39C12' } },
    { name: '广州', value: [113.264, 23.129, 76], itemStyle: { color: '#9B59B6' } },
    { name: '深圳', value: [114.057, 22.543, 82], itemStyle: { color: '#1ABC9C' } },
  ];

  // 初始化地图
  function initMap() {
    const option = {
      backgroundColor: '#0a1929',
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        textStyle: { 
          color: '#fff',
          fontSize: 14
        },
        formatter: function (params) {
          if (params.seriesType === 'scatter' || params.seriesType === 'effectScatter') {
            return `<div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${params.name}</div>
              <div>活跃度: <span style="color: #26B99A;">${params.value[2]}</span></div>
            </div>`;
          }
          return params.name;
        },
      },
      geo: {
        map: 'china',
        roam: true,
        zoom: 1.2,
        center: [107, 36],
        scaleLimit: {
          min: 0.8,
          max: 3,
        },
        itemStyle: {
          areaColor: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(46, 134, 193, 0.6)' },
              { offset: 1, color: 'rgba(11, 36, 59, 0.8)' },
            ],
          },
          borderColor: 'rgba(255, 255, 255, 0.4)',
          borderWidth: 1,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
          shadowBlur: 15,
        },
        emphasis: {
          itemStyle: {
            areaColor: 'rgba(46, 134, 193, 0.9)',
            borderColor: 'rgba(255, 255, 255, 0.8)',
            borderWidth: 2,
          },
        },
      },
      series: [
        {
          type: 'scatter',
          coordinateSystem: 'geo',
          data: mapData,
          symbolSize: function (val) {
            return Math.max(val[2] / 3, 20);
          },
          symbol: 'circle',
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(255, 255, 255, 0.6)',
          },
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: 12,
            fontWeight: 'bold',
            distance: 10,
            formatter: '{b}',
          },
          emphasis: {
            scale: 1.3,
            itemStyle: {
              shadowBlur: 25,
              shadowColor: 'rgba(255, 255, 255, 0.9)',
            },
            label: {
              show: true,
              fontSize: 14,
              color: '#26B99A',
            },
          },
        },
        {
          type: 'effectScatter',
          coordinateSystem: 'geo',
          data: mapData,
          symbolSize: function (val) {
            return Math.max(val[2] / 5, 12);
          },
          showEffectOn: 'render',
          rippleEffect: {
            brushType: 'stroke',
            scale: 3,
            period: 3,
          },
          itemStyle: {
            color: 'rgba(255, 255, 255, 0.9)',
            shadowBlur: 15,
            shadowColor: 'rgba(255, 255, 255, 0.7)',
          },
          zlevel: 1,
        },
      ],
    };
    
    console.log('设置地图选项:', option);
    setOptions(option);
  }

  // 加载中国地图数据
  async function loadChinaMapData() {
    try {
      mapStatus.value = '加载中...';
      console.log('开始加载地图数据...');
      
      const response = await fetch('/json/100000_full.json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const geoJson = await response.json();
      console.log('地图数据加载成功:', geoJson);
      
      // 注册地图数据到 ECharts
      echarts.registerMap('china', geoJson);
      console.log('地图数据注册成功');
      
      // 初始化地图
      initMap();
      mapStatus.value = '加载成功';
    } catch (error) {
      console.error('加载中国地图数据失败:', error);
      mapStatus.value = `加载失败: ${error.message}`;
    }
  }

  function reloadMap() {
    loadChinaMapData();
  }

  onMounted(() => {
    setTimeout(() => {
      loadChinaMapData();
    }, 100);
  });
</script>

<style scoped>
  .test-map-simple {
    padding: 20px;
    background: #0a1929;
    min-height: 100vh;
    color: #fff;
  }

  .test-map-simple h2 {
    text-align: center;
    color: #fff;
    margin-bottom: 20px;
  }

  .status-info {
    margin-bottom: 20px;
    text-align: center;
  }

  .status-success {
    color: #26B99A;
  }

  .status-loading {
    color: #F39C12;
  }

  .status-pending {
    color: #95A5A6;
  }

  .status-error {
    color: #E74C3C;
  }

  .reload-btn {
    margin-left: 10px;
    padding: 8px 16px;
    background: #3498DB;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
  }

  .reload-btn:hover {
    background: #2980B9;
  }

  .map-wrapper {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    overflow: hidden;
  }

  .map-chart {
    width: 100%;
    height: 600px;
    background: #0a1929;
  }
</style>
