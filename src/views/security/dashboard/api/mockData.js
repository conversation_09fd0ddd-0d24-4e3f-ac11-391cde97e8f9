// 大屏仪表板Mock数据

// 业务异常情况统计数据
export const businessExceptionMockData = {
  '7': {
    dates: ['10-18', '10-19', '10-20', '10-21', '10-22', '10-23', '10-24'],
    smartWork: [12, 15, 8, 20, 18, 25, 22],
    smartMaintenance: [8, 12, 6, 15, 14, 18, 16],
    smartWarehouse: [5, 8, 4, 10, 9, 12, 11]
  },
  '15': {
    dates: ['10-10', '10-11', '10-12', '10-13', '10-14', '10-15', '10-16', '10-17', '10-18', '10-19', '10-20', '10-21', '10-22', '10-23', '10-24'],
    smartWork: [10, 12, 15, 8, 20, 18, 25, 22, 12, 15, 8, 20, 18, 25, 22],
    smartMaintenance: [6, 8, 12, 6, 15, 14, 18, 16, 8, 12, 6, 15, 14, 18, 16],
    smartWarehouse: [3, 5, 8, 4, 10, 9, 12, 11, 5, 8, 4, 10, 9, 12, 11]
  },
  '30': {
    dates: Array.from({length: 30}, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return `${date.getMonth() + 1}-${String(date.getDate()).padStart(2, '0')}`;
    }),
    smartWork: Array.from({length: 30}, () => Math.floor(Math.random() * 20) + 10),
    smartMaintenance: Array.from({length: 30}, () => Math.floor(Math.random() * 15) + 8),
    smartWarehouse: Array.from({length: 30}, () => Math.floor(Math.random() * 10) + 5)
  }
};

// 工程车辆检修情况统计数据
export const vehicleInspectionMockData = {
  '1': {
    weeks: ['第一周', '第二周', '第三周', '第四周'],
    inspectionCount: [45, 52, 38, 48],
    exceptionCount: [8, 12, 6, 10]
  },
  '3': {
    weeks: ['第一月', '第二月', '第三月'],
    inspectionCount: [180, 195, 165],
    exceptionCount: [32, 38, 28]
  },
  '6': {
    weeks: ['第一季度', '第二季度'],
    inspectionCount: [540, 580],
    exceptionCount: [95, 105]
  }
};

// 模型总调用趋势数据
export const modelCallMockData = {
  '1': {
    dates: ['10-01', '10-05', '10-10', '10-15', '10-20', '10-24'],
    calls: [1200, 1350, 1180, 1420, 1380, 1500]
  },
  '3': {
    dates: ['8月', '9月', '10月'],
    calls: [35000, 38000, 42000]
  },
  '6': {
    dates: ['5月', '6月', '7月', '8月', '9月', '10月'],
    calls: [28000, 32000, 35000, 35000, 38000, 42000]
  }
};

// 作业管控情况统计数据
export const workControlMockData = {
  '1': {
    weeks: ['第一周', '第二周', '第三周', '第四周'],
    inspectionCount: [42, 48, 35, 45],
    exceptionCount: [7, 11, 5, 9]
  },
  '3': {
    weeks: ['第一月', '第二月', '第三月'],
    inspectionCount: [165, 180, 170],
    exceptionCount: [28, 35, 30]
  },
  '6': {
    weeks: ['第一季度', '第二季度'],
    inspectionCount: [515, 550],
    exceptionCount: [88, 95]
  }
};

// 关键指标概览数据
export const keyMetricsMockData = [
  {
    title: '今日活跃次数',
    value: '12',
    change: '+8%',
    changeColor: '#E67E22',
    changeIcon: 'ant-design:arrow-up-outlined',
    background: 'linear-gradient(135deg, #E74C3C 0%, #C0392B 100%)',
    icon: 'ant-design:fire-outlined'
  },
  {
    title: '今日作业任务',
    value: '47',
    change: '+8%',
    changeColor: '#3498DB',
    changeIcon: 'ant-design:arrow-up-outlined',
    background: 'linear-gradient(135deg, #26B99A 0%, #1ABC9C 100%)',
    icon: 'ant-design:tool-outlined'
  },
  {
    title: 'AR眼镜使用次数',
    value: '32',
    change: '+5%',
    changeColor: '#5DADE2',
    changeIcon: 'ant-design:arrow-up-outlined',
    background: 'linear-gradient(135deg, #3498DB 0%, #2980B9 100%)',
    icon: 'ant-design:eye-outlined'
  },
  {
    title: '工程车辆检修',
    value: '8',
    change: '30%',
    changeColor: '#95A5A6',
    changeIcon: 'ant-design:percentage-outlined',
    background: 'linear-gradient(135deg, #8E44AD 0%, #7D3C98 100%)',
    icon: 'ant-design:car-outlined'
  }
];

// 地图标记数据
export const mapMarkersMockData = [
  { 
    name: '成都', 
    value: [104.066, 30.572, 85], 
    itemStyle: { color: '#26B99A' },
    details: {
      activeDevices: 12,
      totalTasks: 28,
      arGlasses: 8,
      vehicleInspections: 5
    }
  },
  { 
    name: '石家庄', 
    value: [114.514, 38.042, 72], 
    itemStyle: { color: '#3498DB' },
    details: {
      activeDevices: 8,
      totalTasks: 19,
      arGlasses: 6,
      vehicleInspections: 3
    }
  }
];

// AR眼镜实时画面数据
export const arGlassesMockData = [
  {
    id: 1,
    name: 'AR-001',
    status: 'online',
    location: '成都站台A',
    lastUpdate: '2分钟前',
    streamUrl: '/api/stream/ar001',
    operator: '张三',
    workType: '智慧作业'
  },
  {
    id: 2,
    name: 'AR-002',
    status: 'online',
    location: '成都站台B',
    lastUpdate: '1分钟前',
    streamUrl: '/api/stream/ar002',
    operator: '李四',
    workType: '智慧检修'
  },
  {
    id: 3,
    name: 'AR-003',
    status: 'offline',
    location: '石家庄站台A',
    lastUpdate: '10分钟前',
    streamUrl: null,
    operator: '王五',
    workType: '智慧料库'
  },
  {
    id: 4,
    name: 'AR-004',
    status: 'online',
    location: '石家庄站台B',
    lastUpdate: '30秒前',
    streamUrl: '/api/stream/ar004',
    operator: '赵六',
    workType: '智慧作业'
  },
  {
    id: 5,
    name: 'AR-005',
    status: 'online',
    location: '成都检修区',
    lastUpdate: '1分钟前',
    streamUrl: '/api/stream/ar005',
    operator: '钱七',
    workType: '智慧检修'
  },
  {
    id: 6,
    name: 'AR-006',
    status: 'offline',
    location: '石家庄检修区',
    lastUpdate: '15分钟前',
    streamUrl: null,
    operator: '孙八',
    workType: '智慧检修'
  },
  {
    id: 7,
    name: 'AR-007',
    status: 'online',
    location: '成都料库',
    lastUpdate: '3分钟前',
    streamUrl: '/api/stream/ar007',
    operator: '周九',
    workType: '智慧料库'
  },
  {
    id: 8,
    name: 'AR-008',
    status: 'online',
    location: '石家庄料库',
    lastUpdate: '2分钟前',
    streamUrl: '/api/stream/ar008',
    operator: '吴十',
    workType: '智慧料库'
  }
];

// 实时数据更新函数
export function generateRealTimeData() {
  // 模拟实时数据变化
  const now = new Date();
  const timeStr = now.toLocaleTimeString('zh-CN', { hour12: false });
  
  return {
    timestamp: timeStr,
    activeCount: Math.floor(Math.random() * 5) + 10,
    taskCount: Math.floor(Math.random() * 10) + 40,
    arGlassesCount: Math.floor(Math.random() * 8) + 28,
    vehicleInspectionCount: Math.floor(Math.random() * 3) + 6
  };
}

// 导出数据模拟函数
export function simulateDataExport(dataType, period) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const exportData = {
        type: dataType,
        period: period,
        exportTime: new Date().toISOString(),
        filename: `${dataType}_${period}_${Date.now()}.xlsx`,
        status: 'success'
      };
      resolve(exportData);
    }, 1000);
  });
}

// 地图点击事件数据
export function getLocationDetails(locationName) {
  const locationData = mapMarkersMockData.find(item => item.name === locationName);
  if (locationData) {
    return {
      ...locationData.details,
      name: locationName,
      recentEvents: [
        { time: '10:30', event: 'AR设备上线', type: 'info' },
        { time: '10:25', event: '作业任务完成', type: 'success' },
        { time: '10:20', event: '检修异常报警', type: 'warning' },
        { time: '10:15', event: '设备状态正常', type: 'info' }
      ]
    };
  }
  return null;
}
