<template>
  <div class="ar-glasses-panel">
    <div class="panel-header">
      <div class="header-left">
        <h3 class="panel-title">AR眼镜实时画面</h3>
        <div class="online-status">
          <span class="status-dot online"></span>
          <span class="status-text">在线设备: {{ onlineCount }}/{{ totalCount }}</span>
        </div>
      </div>

      <div class="header-right">
        <Button type="primary" size="small" @click="refreshStreams">
          <Icon icon="ant-design:reload-outlined" />
          刷新
        </Button>
        <Button type="primary" size="small" @click="toggleFullscreen">
          <Icon icon="ant-design:fullscreen-outlined" />
          全屏
        </Button>
      </div>
    </div>

    <div class="streams-container" ref="streamsContainerRef">
      <div class="streams-wrapper" :style="{ transform: `translateX(${translateX}px)` }">
        <div
          v-for="(stream, index) in streamsData"
          :key="stream.id"
          class="stream-card"
          @click="selectStream(stream)"
          :class="{ active: selectedStreamId === stream.id }"
        >
          <div class="stream-header">
            <span class="stream-title">{{ stream.name }}</span>
            <div class="stream-status" :class="stream.status">
              <span class="status-dot" :class="stream.status"></span>
              <span class="status-text">{{ stream.status === 'online' ? '在线' : '离线' }}</span>
            </div>
          </div>

          <div class="stream-content">
            <div v-if="stream.status === 'online'" class="video-placeholder">
              <LivePlayer v-if="stream.streamUrl" :path="stream.streamUrl" class="live-player" />
              <div v-else class="placeholder-content">
                <Icon icon="ant-design:video-camera-outlined" :size="48" />
                <span>视频加载中...</span>
              </div>
            </div>
            <div v-else class="offline-placeholder">
              <Icon icon="ant-design:disconnect-outlined" :size="48" />
              <span>设备离线</span>
            </div>
          </div>

          <div class="stream-footer">
            <div class="stream-info">
              <span class="info-item">位置: {{ stream.location }}</span>
              <span class="info-item">{{ stream.lastUpdate }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导航按钮 -->
    <div class="navigation-controls">
      <Button type="text" :disabled="translateX >= 0" @click="scrollLeft" class="nav-btn">
        <Icon icon="ant-design:left-outlined" :size="20" />
      </Button>

      <div class="pagination-dots">
        <span
          v-for="(dot, index) in paginationDots"
          :key="index"
          class="dot"
          :class="{ active: index === currentPage }"
          @click="goToPage(index)"
        ></span>
      </div>

      <Button
        type="text"
        :disabled="translateX <= maxTranslateX"
        @click="scrollRight"
        class="nav-btn"
      >
        <Icon icon="ant-design:right-outlined" :size="20" />
      </Button>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted } from 'vue';
  import { Button, message } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import LivePlayer from '@/components/LivePlayer/index.vue';

  const streamsContainerRef = ref();
  const translateX = ref(0);
  const selectedStreamId = ref(null);
  const containerWidth = ref(0);
  const cardWidth = 280; // 每个卡片的宽度
  const cardGap = 20; // 卡片间距
  const visibleCards = ref(4); // 可见卡片数量
  const currentPage = ref(0);

  // Mock数据
  const streamsData = ref([
    {
      id: 1,
      name: 'AR-001',
      status: 'online',
      location: '成都站台A',
      lastUpdate: '2分钟前',
      streamUrl: '/api/stream/ar001',
    },
    {
      id: 2,
      name: 'AR-002',
      status: 'online',
      location: '成都站台B',
      lastUpdate: '1分钟前',
      streamUrl: '/api/stream/ar002',
    },
    {
      id: 3,
      name: 'AR-003',
      status: 'offline',
      location: '石家庄站台A',
      lastUpdate: '10分钟前',
      streamUrl: null,
    },
    {
      id: 4,
      name: 'AR-004',
      status: 'online',
      location: '石家庄站台B',
      lastUpdate: '30秒前',
      streamUrl: '/api/stream/ar004',
    },
    {
      id: 5,
      name: 'AR-005',
      status: 'online',
      location: '成都检修区',
      lastUpdate: '1分钟前',
      streamUrl: '/api/stream/ar005',
    },
    {
      id: 6,
      name: 'AR-006',
      status: 'offline',
      location: '石家庄检修区',
      lastUpdate: '15分钟前',
      streamUrl: null,
    },
    {
      id: 7,
      name: 'AR-007',
      status: 'online',
      location: '成都料库',
      lastUpdate: '3分钟前',
      streamUrl: '/api/stream/ar007',
    },
    {
      id: 8,
      name: 'AR-008',
      status: 'online',
      location: '石家庄料库',
      lastUpdate: '2分钟前',
      streamUrl: '/api/stream/ar008',
    },
  ]);

  // 计算属性
  const onlineCount = computed(
    () => streamsData.value.filter((stream) => stream.status === 'online').length,
  );

  const totalCount = computed(() => streamsData.value.length);

  const maxTranslateX = computed(() => {
    const totalWidth = streamsData.value.length * (cardWidth + cardGap) - cardGap;
    return Math.min(0, containerWidth.value - totalWidth);
  });

  const paginationDots = computed(() => {
    const totalPages = Math.ceil(streamsData.value.length / visibleCards.value);
    return Array.from({ length: totalPages }, (_, i) => i);
  });

  // 方法
  function updateContainerWidth() {
    if (streamsContainerRef.value) {
      containerWidth.value = streamsContainerRef.value.clientWidth;
      visibleCards.value = Math.floor(containerWidth.value / (cardWidth + cardGap));
    }
  }

  function scrollLeft() {
    const step = (cardWidth + cardGap) * visibleCards.value;
    translateX.value = Math.min(0, translateX.value + step);
    updateCurrentPage();
  }

  function scrollRight() {
    const step = (cardWidth + cardGap) * visibleCards.value;
    translateX.value = Math.max(maxTranslateX.value, translateX.value - step);
    updateCurrentPage();
  }

  function goToPage(pageIndex) {
    const step = (cardWidth + cardGap) * visibleCards.value;
    translateX.value = Math.max(maxTranslateX.value, -pageIndex * step);
    currentPage.value = pageIndex;
  }

  function updateCurrentPage() {
    const step = (cardWidth + cardGap) * visibleCards.value;
    currentPage.value = Math.round(-translateX.value / step);
  }

  function selectStream(stream) {
    selectedStreamId.value = stream.id;
    message.info(`选择了设备: ${stream.name}`);
  }

  function refreshStreams() {
    message.success('刷新成功');
    // 这里可以重新获取数据
  }

  function toggleFullscreen() {
    message.info('切换全屏模式');
  }

  // 生命周期
  onMounted(() => {
    updateContainerWidth();
    window.addEventListener('resize', updateContainerWidth);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', updateContainerWidth);
  });
</script>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 1200px) {
    .panel-header {
      flex-direction: column;
      align-items: flex-start;
      padding: 15px;
      gap: 15px;
    }

    .header-right {
      align-self: flex-end;
    }

    .stream-card {
      width: 240px;
    }
  }

  @media (max-width: 768px) {
    .stream-card {
      width: 200px;
    }

    .stream-content {
      height: 120px;
    }
  }

  .ar-glasses-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    /* border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(11, 36, 59, 90%) 0%, rgba(46, 134, 193, 60%) 100%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 30%);
    backdrop-filter: blur(10px); */
  }

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 10%);
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .panel-title {
    margin: 0;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .online-status {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .status-dot.online {
    background-color: #27ae60;
    box-shadow: 0 0 10px rgba(39, 174, 96, 60%);
  }

  .status-text {
    color: rgba(255, 255, 255, 90%);
    font-size: 14px;
  }

  .header-right {
    display: flex;
    gap: 12px;
  }

  .streams-container {
    flex: 1;
    padding: 20px;
    overflow: hidden;
  }

  .streams-wrapper {
    display: flex;
    gap: 20px;
    transition: transform 0.3s ease;
  }

  .stream-card {
    flex-shrink: 0;
    width: 280px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 20%);
    border-radius: 8px;
    background: rgba(255, 255, 255, 10%);
    cursor: pointer;
  }

  .stream-card:hover,
  .stream-card.active {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 40%);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 40%);
  }

  .stream-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 10%);
  }

  .stream-title {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
  }

  .stream-status {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .stream-status .status-dot.online {
    background-color: #27ae60;
  }

  .stream-status .status-dot.offline {
    background-color: #95a5a6;
  }

  .stream-status .status-text {
    font-size: 12px;
  }

  .stream-content {
    position: relative;
    height: 160px;
    background: rgba(0, 0, 0, 30%);
  }

  .live-player {
    width: 100%;
    height: 100%;
  }

  .placeholder-content,
  .offline-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: rgba(255, 255, 255, 60%);
    gap: 8px;
  }

  .stream-footer {
    padding: 12px;
  }

  .stream-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .info-item {
    color: rgba(255, 255, 255, 70%);
    font-size: 12px;
  }

  .navigation-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    border-top: 1px solid rgba(255, 255, 255, 10%);
    gap: 20px;
  }

  .nav-btn {
    border: 1px solid rgba(255, 255, 255, 20%) !important;
    color: rgba(255, 255, 255, 80%) !important;
  }

  .nav-btn:hover {
    border-color: rgba(255, 255, 255, 40%) !important;
    color: #fff !important;
  }

  .pagination-dots {
    display: flex;
    gap: 8px;
  }

  .dot {
    width: 8px;
    height: 8px;
    transition: all 0.3s ease;
    border-radius: 50%;
    background: rgba(255, 255, 255, 30%);
    cursor: pointer;
  }

  .dot.active {
    transform: scale(1.2);
    background: #fff;
  }
</style>
